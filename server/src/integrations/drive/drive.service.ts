import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { google, drive_v3 } from 'googleapis';
import { Readable } from 'stream';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class DriveService {
  private drive: drive_v3.Drive;
  private rootFolderId: string;
  private readonly logger = new Logger(DriveService.name);

  constructor() {
    try {
      const auth = new google.auth.GoogleAuth({
        scopes: ['https://www.googleapis.com/auth/drive'],
        keyFile: process.env.GOOGLE_SERVICE_ACCOUNT_PATH,
        credentials: process.env.GOOGLE_SERVICE_ACCOUNT_JSON
          ? JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_JSON)
          : undefined,
      });

      this.drive = google.drive({ version: 'v3', auth });

      this.rootFolderId = process.env.DRIVE_ROOT_FOLDER_ID!;
      if (!this.rootFolderId) {
        throw new Error('DRIVE_ROOT_FOLDER_ID não configurado');
      }

      this.logger.log('DriveService inicializado com sucesso');
      
      // Verificar acesso à pasta raiz na inicialização
      this.verifyRootFolderAccess();
    } catch (error) {
      this.logger.error('Erro ao inicializar DriveService:', error);
      throw error;
    }
  }

  private async getOrCreateFolder(operationId: string): Promise<string> {
    try {
      // Verificar acesso à pasta raiz antes de prosseguir
      const hasRootAccess = await this.checkFileAccess(this.rootFolderId, 'pasta raiz');
      if (!hasRootAccess) {
        throw new Error(`Sem acesso à pasta raiz ${this.rootFolderId}. Verifique se a pasta foi compartilhada com a conta de serviço.`);
      }

      // Busca pasta existente
      const searchResponse = await this.drive.files.list({
        q: `"${this.rootFolderId}" in parents and name = "${operationId}" and mimeType = 'application/vnd.google-apps.folder' and trashed = false`,
        fields: 'files(id,name)',
      });

      const files = searchResponse.data.files ?? [];
      if (files.length > 0) {
        this.logger.log(
          `Pasta existente encontrada para operação ${operationId}`,
        );
        return files[0].id!;
      }

      const metadata: drive_v3.Schema$File = {
        name: operationId,
        mimeType: 'application/vnd.google-apps.folder',
        parents: [this.rootFolderId],
      };

      const createResponse = await this.drive.files.create({
        requestBody: metadata,
        fields: 'id',
      });

      const folderId = createResponse.data.id!;

      return folderId;
    } catch (error) {
      this.logger.error(`Falha ao obter/criar pasta ${operationId}:`, error);
      
      // Adicionar informações mais específicas sobre o erro
      if (error.code === 404) {
        throw new InternalServerErrorException(
          `Pasta raiz não encontrada (${this.rootFolderId}). Verifique se a pasta foi compartilhada com a conta de serviço.`
        );
      } else if (error.code === 403) {
        throw new InternalServerErrorException(
          'Sem permissão para acessar o Google Drive. Verifique as credenciais da conta de serviço.'
        );
      }
      
      throw new InternalServerErrorException(
        'Erro no Drive ao criar pasta da operação',
      );
    }
  }

  async saveContractPdf(
    operationId: string,
    contractPdf: Express.Multer.File,
  ): Promise<string> {
    try {
      // Validar se o arquivo tem conteúdo
      if (!contractPdf || !contractPdf.buffer || contractPdf.buffer.length === 0) {
        throw new Error('Arquivo PDF inválido ou vazio');
      }

      const bufferStart = contractPdf.buffer.subarray(0, 10).toString();
      if (!bufferStart.startsWith('%PDF')) {
        throw new Error('Arquivo recebido não é um PDF válido');
      }

      const folderId = await this.getOrCreateFolder(operationId);

      const fileName = `contrato-${Date.now()}.pdf`;
      const metadata: drive_v3.Schema$File = {
        name: fileName,
        parents: [folderId],
      };

      // Criar stream usando Readable.from() que é mais moderno e confiável
      const stream = Readable.from(contractPdf.buffer);

      const uploadResponse = await this.drive.files.create({
        requestBody: metadata,
        media: {
          mimeType: 'application/pdf',
          body: stream,
        },
        fields: 'id',
      });

      const fileId = uploadResponse.data.id!;

      // Define permissões de leitura
      await this.drive.permissions.create({
        fileId,
        requestBody: {
          role: 'reader',
          type: 'anyone',
        },
      });

      // Busca links do arquivo
      const fileInfo = await this.drive.files.get({
        fileId,
        fields: 'webViewLink,webContentLink,size',
      });

      const fileUrl =
        fileInfo.data.webContentLink || fileInfo.data.webViewLink!;

      return fileUrl;
    } catch (error) {
      this.logger.error(
        `Erro ao salvar contrato no Drive para ${operationId}:`,
        error,
      );
      throw new InternalServerErrorException(
        'Falha ao salvar contrato no Drive',
      );
    }
  }

  async saveIdentityDoc(
    operationId: string,
    identityDoc: Express.Multer.File,
  ): Promise<string> {
    try {
      if (!identityDoc?.buffer || identityDoc.buffer.length === 0) {
        throw new Error('Documento de identidade inválido ou vazio');
      }

      this.logger.log(
        `Salvando documento de identidade para operação ${operationId} (${identityDoc.buffer.length} bytes)`,
      );

      const folderId = await this.getOrCreateFolder(operationId);

      const fileName = `identidade-${Date.now()}-${identityDoc.originalname}`;
      const metadata: drive_v3.Schema$File = {
        name: fileName,
        parents: [folderId],
      };

      // Criar stream usando Readable.from() que é mais moderno e confiável
      const stream = Readable.from(identityDoc.buffer);

      const uploadResponse = await this.drive.files.create({
        requestBody: metadata,
        media: {
          mimeType: identityDoc.mimetype,
          body: stream,
        },
        fields: 'id',
      });

      const fileId = uploadResponse.data.id!;

      // Define permissões de leitura
      await this.drive.permissions.create({
        fileId,
        requestBody: {
          role: 'reader',
          type: 'anyone',
        },
      });

      // Busca links do arquivo
      const fileInfo = await this.drive.files.get({
        fileId,
        fields: 'webViewLink,webContentLink',
      });

      const fileUrl =
        fileInfo.data.webContentLink || fileInfo.data.webViewLink!;

      this.logger.log(
        `Documento de identidade salvo com sucesso para operação ${operationId}: ${fileId}`,
      );
      return fileUrl;
    } catch (error) {
      this.logger.error(
        `Erro ao salvar documento de identidade no Drive para ${operationId}:`,
        error,
      );
      throw new InternalServerErrorException(
        'Falha ao salvar documento de identidade no Drive',
      );
    }
  }

  async listOperationFiles(operationId: string): Promise<any[]> {
    try {
      const folderId = await this.getOrCreateFolder(operationId);

      const response = await this.drive.files.list({
        q: `"${folderId}" in parents and trashed = false`,
        fields: 'files(id,name,mimeType,createdTime,size,webViewLink)',
        orderBy: 'createdTime desc',
      });

      return response.data.files ?? [];
    } catch (error) {
      this.logger.error(
        `Erro ao listar arquivos da operação ${operationId}:`,
        error,
      );
      throw new InternalServerErrorException(
        'Erro ao listar arquivos da operação',
      );
    }
  }

  async deleteFile(fileId: string): Promise<void> {
    try {
      await this.drive.files.delete({ fileId });
      this.logger.log(`Arquivo deletado com sucesso: ${fileId}`);
    } catch (error) {
      this.logger.error(`Erro ao deletar arquivo ${fileId}:`, error);
      throw new InternalServerErrorException('Erro ao deletar arquivo');
    }
  }

  /**
   * Deleta uma pasta de operação e todos os seus arquivos
   */
  async deleteOperationFolder(operationId: string): Promise<void> {
    try {
      this.logger.log(`Iniciando limpeza da pasta da operação: ${operationId}`);

      // Busca a pasta da operação
      const searchResponse = await this.drive.files.list({
        q: `"${this.rootFolderId}" in parents and name = "${operationId}" and mimeType = 'application/vnd.google-apps.folder' and trashed = false`,
        fields: 'files(id,name)',
      });

      const folders = searchResponse.data.files ?? [];
      if (folders.length === 0) {
        this.logger.warn(`Pasta da operação ${operationId} não encontrada`);
        return;
      }

      const folderId = folders[0].id!;
      this.logger.log(`Pasta encontrada: ${folderId}`);

      // Lista todos os arquivos na pasta
      const filesResponse = await this.drive.files.list({
        q: `"${folderId}" in parents and trashed = false`,
        fields: 'files(id,name)',
      });

      const files = filesResponse.data.files ?? [];
      this.logger.log(`Encontrados ${files.length} arquivos para deletar`);

      // Deleta todos os arquivos da pasta
      for (const file of files) {
        try {
          await this.drive.files.delete({ fileId: file.id! });
          this.logger.log(`Arquivo deletado: ${file.name} (${file.id})`);
        } catch (error) {
          this.logger.error(`Erro ao deletar arquivo ${file.name}:`, error);
          // Continua deletando outros arquivos mesmo se um falhar
        }
      }

      // Deleta a pasta vazia
      await this.drive.files.delete({ fileId: folderId });
      this.logger.log(`Pasta da operação ${operationId} deletada com sucesso`);

    } catch (error) {
      this.logger.error(`Erro ao deletar pasta da operação ${operationId}:`, error);
      // Não lança erro para não quebrar o fluxo de exclusão da operação
      // A exclusão do banco de dados deve continuar mesmo se a limpeza do Drive falhar
    }
  }

  private async verifyRootFolderAccess(): Promise<void> {
    try {
      const response = await this.drive.files.get({
        fileId: this.rootFolderId,
        fields: 'id,name,mimeType,owners,permissions',
      });

      if (response.data.mimeType !== 'application/vnd.google-apps.folder') {
        throw new Error('O ID fornecido não é de uma pasta');
      }    
    } catch (error) {
      this.logger.error('Erro ao verificar acesso à pasta raiz:', error);
      
      if (error.code === 404) {
        this.logger.error(`A pasta com ID ${this.rootFolderId} não foi encontrada ou a conta de serviço não tem acesso a ela.`);
        this.logger.error('Certifique-se de que:');
        this.logger.error('1. O ID da pasta está correto');
        this.logger.error('2. A pasta foi compartilhada com o email da conta de serviço');
        this.logger.error('3. A conta de serviço tem permissões adequadas');
      }
    }
  }

  private async checkFileAccess(fileId: string, operation: string): Promise<boolean> {
    try {
      await this.drive.files.get({
        fileId,
        fields: 'id',
      });
      return true;
    } catch (error) {
      this.logger.error(`Erro ao verificar acesso para ${operation} (ID: ${fileId}):`, error);
      
      if (error.code === 404) {
        this.logger.error(`Arquivo/pasta não encontrado ou sem acesso: ${fileId}`);
        this.logger.error('Verifique se o arquivo existe e se a conta de serviço tem acesso.');
      }
      
      return false;
    }
  }

}
